/* Premium Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-gold: #f59e0b;
    --secondary-gold: #fbbf24;
    --light-gold: #fef3c7;
    --dark-gold: #d97706;
    --primary-black: #000000;
    --secondary-black: #1a1a1a;
    --light-black: #374151;
    --text-light: #f3f4f6;
    --text-dark: #1f2937;
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
    background: var(--primary-black);
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 3D Canvas Background */
#three-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
}

/* Loading Screen */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-black) 0%, var(--secondary-black) 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

#loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-gear {
    font-size: 4rem;
    color: var(--primary-gold);
    margin-bottom: 2rem;
    animation: spin 2s linear infinite;
}

#loading-screen p {
    color: var(--text-light);
    font-size: 1.2rem;
    font-weight: 500;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Premium Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    z-index: 1000;
    padding: 1rem 0;
    border-bottom: 1px solid var(--glass-border);
    transition: all 0.3s ease;
}

.navbar.scrolled {
    background: rgba(0, 0, 0, 0.95);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 2px solid var(--primary-gold);
    transition: all 0.3s ease;
}

.logo-img:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 0 20px var(--primary-gold);
}

.team-name {
    font-weight: 800;
    font-size: 1.4rem;
    color: var(--primary-gold);
    text-shadow: 0 0 10px rgba(245, 158, 11, 0.3);
}

.team-number {
    font-weight: 600;
    color: var(--secondary-gold);
    font-size: 1.1rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2.5rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-light);
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 0;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-gold);
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-gold);
    text-shadow: 0 0 10px rgba(245, 158, 11, 0.3);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--primary-gold);
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

/* Premium Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 120px 20px 80px;
    background: linear-gradient(135deg, var(--primary-black) 0%, var(--secondary-black) 30%, var(--primary-gold) 70%, var(--dark-gold) 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(245, 158, 11, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.hero-content {
    max-width: 1400px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    z-index: 2;
}

.hero-text {
    text-align: left;
}

.hero-title {
    font-size: 4.5rem;
    font-weight: 900;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #fff, var(--secondary-gold), var(--primary-gold));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(245, 158, 11, 0.3);
    line-height: 1.1;
}

.hero-subtitle {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--secondary-gold);
    text-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
}

.hero-description {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--light-gold);
    font-weight: 500;
}

.hero-tagline {
    font-size: 1.2rem;
    margin-bottom: 2.5rem;
    color: var(--text-light);
    font-style: italic;
    font-weight: 400;
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.btn {
    padding: 15px 30px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    display: inline-block;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-gold);
    color: var(--primary-black);
    border: 2px solid var(--primary-gold);
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
}

.btn-primary:hover {
    background: var(--secondary-gold);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(245, 158, 11, 0.4);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-gold);
    border: 2px solid var(--primary-gold);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: var(--primary-gold);
    color: var(--primary-black);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(245, 158, 11, 0.4);
}

/* Hero Visual */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.hero-image-container {
    position: relative;
    width: 500px;
    height: 500px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-team-photo {
    width: 350px;
    height: 350px;
    object-fit: cover;
    border-radius: 50%;
    border: 4px solid var(--primary-gold);
    box-shadow: 0 0 40px rgba(245, 158, 11, 0.4);
    transition: all 0.3s ease;
    position: absolute;
    z-index: 10 !important;
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    background: var(--secondary-black);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.hero-team-photo:hover {
    transform: translate(-50%, -50%) scale(1.05);
    box-shadow: 0 0 60px rgba(245, 158, 11, 0.6);
}

/* Ensure team photo loads properly */
.hero-team-photo:not([src]),
.hero-team-photo[src=""],
.hero-team-photo[src="#"] {
    background: var(--secondary-black);
    border: 4px solid var(--primary-gold);
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-team-photo:not([src])::before,
.hero-team-photo[src=""]::before,
.hero-team-photo[src="#"]::before {
    content: "🤖";
    font-size: 4rem;
    color: var(--primary-gold);
}

.gear-container {
    position: absolute;
    width: 400px;
    height: 400px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
}

.gear-3d {
    width: 300px;
    height: 300px;
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    border-radius: 50%;
    backdrop-filter: blur(20px);
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    transition: all 0.3s ease;
    opacity: 0.3;
    z-index: 1;
}

.gear-3d::before {
    content: '⚙️';
    font-size: 6rem;
    animation: gearRotate 10s linear infinite;
    opacity: 0.5;
}

.gear-3d:hover {
    transform: scale(1.02);
    box-shadow: 0 0 30px var(--primary-gold);
}

@keyframes gearRotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-particles::before,
.floating-particles::after {
    content: '';
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary-gold);
    border-radius: 50%;
    animation: float 3s ease-in-out infinite;
}

.floating-particles::before {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-particles::after {
    top: 60%;
    right: 15%;
    animation-delay: 1.5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) scale(1); opacity: 0.7; }
    50% { transform: translateY(-20px) scale(1.2); opacity: 1; }
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--text-light);
    font-weight: 500;
    animation: bounce 2s infinite;
}

.scroll-arrow {
    width: 2px;
    height: 30px;
    background: var(--primary-gold);
    margin-bottom: 10px;
    position: relative;
}

.scroll-arrow::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: -3px;
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 8px solid var(--primary-gold);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

/* Section Styles */
.section-title {
    font-size: 3rem;
    font-weight: 800;
    text-align: center;
    margin-bottom: 4rem;
    color: var(--primary-gold);
    text-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(90deg, transparent, var(--primary-gold), transparent);
    border-radius: 2px;
}

/* Mission Section */
.mission {
    padding: 100px 20px;
    background: linear-gradient(135deg, var(--secondary-black) 0%, var(--light-black) 100%);
    color: var(--text-light);
    position: relative;
}

.mission::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 70% 30%, rgba(245, 158, 11, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.mission-statement {
    font-size: 1.3rem;
    line-height: 1.8;
    text-align: center;
    margin-bottom: 4rem;
    color: var(--text-light);
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
    font-weight: 400;
}

.mission-values {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2.5rem;
    margin-top: 4rem;
}

.value-item {
    text-align: center;
    padding: 3rem 2rem;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.value-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.value-item:hover::before {
    opacity: 1;
}

.value-item:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border-color: var(--primary-gold);
}

.value-item i {
    font-size: 3.5rem;
    color: var(--primary-gold);
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.value-item:hover i {
    transform: scale(1.1);
    text-shadow: 0 0 20px var(--primary-gold);
}

.value-item h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-light);
}

.value-item p {
    color: #d1d5db;
    font-weight: 400;
    line-height: 1.6;
}

/* Team Showcase Section */
.team-showcase {
    padding: 100px 20px;
    background: linear-gradient(135deg, var(--primary-black) 0%, var(--secondary-black) 100%);
    color: var(--text-light);
    position: relative;
    overflow: hidden;
}

.team-showcase::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(245, 158, 11, 0.1) 0%, transparent 70%);
    pointer-events: none;
}

.team-photo-container {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.team-photo-container:hover {
    transform: translateY(-10px);
    box-shadow: 0 35px 70px rgba(0, 0, 0, 0.4);
}

.team-showcase-photo {
    width: 100%;
    height: 500px;
    object-fit: cover;
    display: block !important;
    transition: all 0.3s ease;
    opacity: 1 !important;
    visibility: visible !important;
    background: var(--secondary-black);
    border: 2px solid var(--primary-gold);
}

/* Fallback for team showcase photo */
.team-showcase-photo:not([src]),
.team-showcase-photo[src=""],
.team-showcase-photo[src="#"] {
    background: linear-gradient(135deg, var(--secondary-black) 0%, var(--light-black) 100%);
    display: flex !important;
    align-items: center;
    justify-content: center;
    position: relative;
}

.team-showcase-photo:not([src])::before,
.team-showcase-photo[src=""]::before,
.team-showcase-photo[src="#"]::before {
    content: "The Gadgeteers Team";
    color: var(--primary-gold);
    font-size: 2rem;
    font-weight: 700;
    text-align: center;
}

.team-photo-container:hover .team-showcase-photo {
    transform: scale(1.05);
}

.photo-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 3rem 2rem 2rem;
    transform: translateY(100%);
    transition: all 0.3s ease;
}

.team-photo-container:hover .photo-overlay {
    transform: translateY(0);
}

.photo-caption h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-gold);
    margin-bottom: 0.5rem;
    text-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
}

.photo-caption p {
    font-size: 1.1rem;
    color: var(--text-light);
    font-weight: 400;
}

/* Teams Section */
.teams {
    padding: 100px 20px;
    background: var(--primary-black);
    color: var(--text-light);
    position: relative;
}

.team-tabs {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-bottom: 4rem;
}

.tab-button {
    padding: 15px 30px;
    border: 2px solid var(--glass-border);
    background: var(--glass-bg);
    color: var(--text-light);
    border-radius: 12px;
    cursor: pointer;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.tab-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(245, 158, 11, 0.2), transparent);
    transition: left 0.5s ease;
}

.tab-button:hover::before {
    left: 100%;
}

.tab-button.active,
.tab-button:hover {
    background: var(--primary-gold);
    color: var(--primary-black);
    border-color: var(--primary-gold);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
}

.team-content {
    display: none;
    animation: fadeInUp 0.5s ease;
}

.team-content.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.team-year {
    font-size: 2.5rem;
    font-weight: 800;
    text-align: center;
    margin-bottom: 1.5rem;
    color: var(--primary-gold);
    text-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
}

.team-description {
    text-align: center;
    font-size: 1.2rem;
    color: var(--text-light);
    margin-bottom: 3rem;
    font-weight: 400;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
}

.member-card {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.member-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.member-card:hover::before {
    opacity: 1;
}

.member-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border-color: var(--primary-gold);
}

.member-avatar {
    width: 80px;
    height: 80px;
    background: var(--primary-gold);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.member-avatar i {
    font-size: 2.5rem;
    color: var(--primary-black);
}

.member-card:hover .member-avatar {
    transform: scale(1.1);
    box-shadow: 0 0 30px var(--primary-gold);
}

.member-info h4 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-light);
}

.role {
    color: var(--primary-gold);
    font-weight: 600;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.member-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0;
    height: 0;
    background: radial-gradient(circle, var(--primary-gold) 0%, transparent 70%);
    opacity: 0;
    transition: all 0.3s ease;
    border-radius: 50%;
}

.member-card:hover .member-glow {
    width: 200px;
    height: 200px;
    opacity: 0.1;
}

/* Accomplishments Section */
.accomplishments {
    padding: 100px 20px;
    background: linear-gradient(135deg, var(--secondary-black) 0%, var(--light-black) 100%);
    color: var(--text-light);
    position: relative;
}

.accomplishments::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(245, 158, 11, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.accomplishments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 3rem;
    max-width: 1000px;
    margin: 0 auto;
}

.achievement-card {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 24px;
    padding: 3rem;
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.achievement-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.achievement-card:hover::before {
    opacity: 1;
}

.achievement-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    border-color: var(--primary-gold);
}

.achievement-year {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    background: var(--primary-black);
    color: var(--primary-gold);
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    font-weight: 800;
    font-size: 1rem;
    border: 2px solid var(--primary-gold);
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
}

.achievement-content {
    text-align: center;
}

.achievement-content i {
    font-size: 4rem;
    color: var(--primary-gold);
    margin-bottom: 1.5rem;
    text-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
}

.achievement-content h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-light);
}

.achievement-status {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--primary-gold);
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.achievement-description {
    color: #d1d5db;
    line-height: 1.7;
    font-weight: 400;
}

.achievement-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0;
    height: 0;
    background: radial-gradient(circle, var(--primary-gold) 0%, transparent 70%);
    opacity: 0;
    transition: all 0.3s ease;
    border-radius: 50%;
}

.achievement-card:hover .achievement-glow {
    width: 300px;
    height: 300px;
    opacity: 0.1;
}

/* Contact Section */
.contact {
    padding: 100px 20px;
    background: var(--primary-black);
    color: var(--text-light);
}

.contact-content {
    max-width: 1200px;
    margin: 0 auto;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.contact-info {
    text-align: left;
}

.contact-info h3 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--primary-gold);
    text-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
}

.contact-info p {
    font-size: 1.2rem;
    color: var(--text-light);
    margin-bottom: 3rem;
    font-weight: 400;
    line-height: 1.6;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1.1rem;
    padding: 1rem 2rem;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    border-color: var(--primary-gold);
}

.contact-item i {
    color: var(--primary-gold);
    width: 25px;
    font-size: 1.3rem;
}

.contact-item a {
    color: var(--primary-gold);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.contact-item a:hover {
    text-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
}

.contact-image {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.contact-image:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
}

.contact-team-photo {
    width: 100%;
    height: 400px;
    object-fit: cover;
    display: block !important;
    transition: all 0.3s ease;
    opacity: 1 !important;
    visibility: visible !important;
    background: var(--secondary-black);
    border: 2px solid var(--primary-gold);
}

/* Fallback for contact team photo */
.contact-team-photo:not([src]),
.contact-team-photo[src=""],
.contact-team-photo[src="#"] {
    background: linear-gradient(135deg, var(--secondary-black) 0%, var(--light-black) 100%);
    display: flex !important;
    align-items: center;
    justify-content: center;
}

.contact-team-photo:not([src])::before,
.contact-team-photo[src=""]::before,
.contact-team-photo[src="#"]::before {
    content: "Join Our Team!";
    color: var(--primary-gold);
    font-size: 1.5rem;
    font-weight: 700;
}

.contact-image:hover .contact-team-photo {
    transform: scale(1.05);
}

.contact-image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 2rem;
    transform: translateY(100%);
    transition: all 0.3s ease;
}

.contact-image:hover .contact-image-overlay {
    transform: translateY(0);
}

.contact-image-overlay p {
    color: var(--primary-gold);
    font-weight: 600;
    font-size: 1.1rem;
    margin: 0;
    text-align: center;
}

/* Footer */
.footer {
    background: var(--primary-black);
    color: var(--primary-gold);
    padding: 3rem 20px;
    text-align: center;
    border-top: 3px solid var(--primary-gold);
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary-gold), transparent);
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
}

.footer-logo {
    margin-bottom: 2rem;
}

.footer-logo .team-name {
    color: var(--primary-gold);
    font-size: 1.5rem;
    font-weight: 800;
    text-shadow: 0 0 15px rgba(245, 158, 11, 0.3);
}

.footer-logo .team-number {
    color: var(--secondary-gold);
    font-weight: 600;
    font-size: 1.2rem;
}

.footer-text p {
    color: #d1d5db;
    margin-bottom: 0.5rem;
    font-weight: 400;
}

/* Scroll Animations */
[data-scroll-animation] {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease;
}

[data-scroll-animation].animate {
    opacity: 1;
    transform: translateY(0);
}

/* CRITICAL: Ensure team photos are ALWAYS visible */
.hero-team-photo,
.team-showcase-photo,
.contact-team-photo {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

/* Prevent gear from overriding team photo */
.hero-team-photo {
    z-index: 10 !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
}

.hero-team-photo:hover {
    transform: translate(-50%, -50%) scale(1.05) !important;
}

/* Keep gear behind and subtle */
.gear-3d {
    z-index: 1 !important;
    opacity: 0.3 !important;
}

.gear-3d::before {
    opacity: 0.5 !important;
}

/* Override any potential hiding from animations */
.hero-team-photo[style*="opacity: 0"],
.team-showcase-photo[style*="opacity: 0"],
.contact-team-photo[style*="opacity: 0"] {
    opacity: 1 !important;
}

.hero-team-photo[style*="visibility: hidden"],
.team-showcase-photo[style*="visibility: hidden"],
.contact-team-photo[style*="visibility: hidden"] {
    visibility: visible !important;
}

.hero-team-photo[style*="display: none"],
.team-showcase-photo[style*="display: none"],
.contact-team-photo[style*="display: none"] {
    display: block !important;
}

/* Ensure team photo containers are visible */
.hero-image-container,
.team-photo-container,
.contact-image {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 3rem;
    }

    .hero-title {
        font-size: 3.5rem;
    }

    .hero-image-container {
        width: 400px;
        height: 400px;
    }

    .hero-team-photo {
        width: 280px;
        height: 280px;
    }

    .gear-container {
        width: 300px;
        height: 300px;
    }

    .gear-3d {
        width: 250px;
        height: 250px;
    }

    .gear-3d::before {
        font-size: 6rem;
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .contact-info {
        text-align: center;
    }
}

@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 80px;
        flex-direction: column;
        background: rgba(0, 0, 0, 0.95);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        backdrop-filter: blur(20px);
        padding: 2rem 0;
        border-top: 1px solid var(--glass-border);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .hero-title {
        font-size: 2.8rem;
    }

    .hero-subtitle {
        font-size: 1.8rem;
    }

    .section-title {
        font-size: 2.2rem;
    }

    .accomplishments-grid {
        grid-template-columns: 1fr;
    }

    .team-grid {
        grid-template-columns: 1fr;
    }

    .mission-values {
        grid-template-columns: 1fr;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
    }

    .gear-container {
        width: 250px;
        height: 250px;
    }

    .gear-3d {
        width: 200px;
        height: 200px;
    }

    .gear-3d::before {
        font-size: 4rem;
    }

    .hero-image-container {
        width: 300px;
        height: 300px;
    }

    .hero-team-photo {
        width: 220px;
        height: 220px;
    }

    .team-showcase-photo {
        height: 300px;
    }

    .contact-team-photo {
        height: 250px;
    }
}

/* Mentors Page Styles */
.mentors-hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 120px 20px 80px;
    background: linear-gradient(135deg, var(--primary-black) 0%, var(--secondary-black) 30%, var(--primary-gold) 70%, var(--dark-gold) 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.mentors-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 70% 30%, rgba(245, 158, 11, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.mentors-hero-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    z-index: 2;
}

.mentors-title {
    font-size: 4rem;
    font-weight: 900;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #fff, var(--secondary-gold), var(--primary-gold));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(245, 158, 11, 0.3);
    line-height: 1.1;
}

.mentors-subtitle {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--secondary-gold);
    text-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
}

.mentors-description {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    color: var(--text-light);
    font-weight: 400;
    line-height: 1.6;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.mentors-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    border-color: var(--primary-gold);
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-gold);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    color: var(--text-light);
    font-weight: 500;
}

/* Mentors Grid Section */
.mentors-grid-section {
    padding: 100px 20px;
    background: var(--secondary-black);
    color: var(--text-light);
}

.mentors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
    margin-top: 4rem;
}

.mentor-card {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.mentor-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.mentor-card:hover::before {
    opacity: 1;
}

.mentor-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    border-color: var(--primary-gold);
}

.mentor-avatar {
    width: 100px;
    height: 100px;
    background: var(--primary-gold);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    transition: all 0.3s ease;
}

.mentor-avatar i {
    font-size: 3rem;
    color: var(--primary-black);
}

.mentor-card:hover .mentor-avatar {
    transform: scale(1.1);
    box-shadow: 0 0 30px var(--primary-gold);
}

.mentor-name {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-light);
}

.mentor-role {
    color: var(--primary-gold);
    font-weight: 600;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 1rem;
}

.mentor-description {
    color: #d1d5db;
    font-weight: 400;
    line-height: 1.6;
    font-size: 1rem;
}

.mentor-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0;
    height: 0;
    background: radial-gradient(circle, var(--primary-gold) 0%, transparent 70%);
    opacity: 0;
    transition: all 0.3s ease;
    border-radius: 50%;
}

.mentor-card:hover .mentor-glow {
    width: 250px;
    height: 250px;
    opacity: 0.1;
}

/* Mentorship Philosophy Section */
.mentorship-philosophy {
    padding: 100px 20px;
    background: linear-gradient(135deg, var(--light-black) 0%, var(--secondary-black) 100%);
    color: var(--text-light);
}

.philosophy-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
    margin-top: 3rem;
}

.philosophy-text h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-gold);
    margin-bottom: 1.5rem;
}

.philosophy-text p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-light);
    margin-bottom: 1.5rem;
}

.philosophy-features {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.feature-item {
    text-align: center;
    padding: 2rem 1.5rem;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    border-color: var(--primary-gold);
}

.feature-item i {
    font-size: 2.5rem;
    color: var(--primary-gold);
    margin-bottom: 1rem;
}

.feature-item h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 0.5rem;
}

.feature-item p {
    font-size: 0.9rem;
    color: #d1d5db;
    line-height: 1.5;
}

/* Mentors CTA Section */
.mentors-cta {
    padding: 100px 20px;
    background: var(--primary-black);
    color: var(--text-light);
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-gold);
    margin-bottom: 1rem;
    text-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
}

.cta-content p {
    font-size: 1.2rem;
    color: var(--text-light);
    margin-bottom: 2.5rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive Design for Mentors Page */
@media (max-width: 1024px) {
    .mentors-title {
        font-size: 3rem;
    }

    .philosophy-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .philosophy-features {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .mentors-title {
        font-size: 2.5rem;
    }

    .mentors-subtitle {
        font-size: 1.4rem;
    }

    .mentors-stats {
        grid-template-columns: 1fr;
    }

    .mentors-grid {
        grid-template-columns: 1fr;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2.2rem;
    }

    .hero-subtitle {
        font-size: 1.4rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .container {
        padding: 0 15px;
    }

    .member-card,
    .achievement-card,
    .value-item {
        padding: 1.5rem;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
}
