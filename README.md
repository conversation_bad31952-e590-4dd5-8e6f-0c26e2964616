# The Gadgeteers - FRC Team 9670 Website

A modern, responsive website for The Gadgeteers robotics team featuring team information, accomplishments, and member details.

## Features

- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Modern UI**: Clean, professional design with smooth animations
- **Team Sections**: Dedicated pages for 2024 and 2025 teams
- **Accomplishments**: Highlighting team achievements and awards
- **Mission Statement**: Inspiring message about the team's goals
- **Interactive Elements**: Smooth scrolling, hover effects, and animations

## File Structure

```
gadgeteers-website/
├── index.html          # Main HTML file
├── styles.css          # CSS styling
├── script.js           # JavaScript functionality
├── logo.svg            # Team logo (placeholder)
└── README.md           # This file
```

## Team Information Included

### 2025 Team Members
- **Gaby** - President
- **Miriam** - Drive Team
- **<PERSON><PERSON><PERSON>** - Drive Team
- **Sarvesh** - Engineering Head
- **Mya** - Project Manager
- **Richard** - Robot Technician & Building Head
- **Sage** - Human Player
- **Jack** - Human Player
- **Eli** - Hardware Expert

### Accomplishments
- **2024**: Runner-up for Rookie All Star Award
- **2025**: Second Overall in competition

## Customization

### Updating the Logo
1. Replace `logo.svg` with your actual team logo
2. Supported formats: SVG, PNG, JPG
3. Recommended size: 100x100 pixels

### Adding Team Photos
1. Create an `images` folder
2. Add team member photos
3. Update the HTML in the team sections to include photos

### Modifying Content
- **Team Members**: Edit the team sections in `index.html`
- **Accomplishments**: Update the accomplishments section
- **Mission Statement**: Modify the mission section text
- **Colors**: Change the color scheme in `styles.css`

## Deployment Options

### Option 1: GitHub Pages (Free)
1. Create a GitHub repository
2. Upload all files to the repository
3. Enable GitHub Pages in repository settings
4. Your site will be available at `https://yourusername.github.io/repository-name`

### Option 2: Netlify (Free)
1. Create account at netlify.com
2. Drag and drop your website folder
3. Get instant deployment with custom domain options

### Option 3: Local Hosting
- Simply open `index.html` in any web browser
- Perfect for testing and local viewing

## Browser Compatibility

- Chrome (recommended)
- Firefox
- Safari
- Edge
- Mobile browsers

## Technical Details

- **Framework**: Vanilla HTML, CSS, JavaScript (no dependencies)
- **Responsive**: Mobile-first design approach
- **Performance**: Optimized for fast loading
- **Accessibility**: Semantic HTML and proper contrast ratios

## Future Enhancements

Consider adding:
- Photo gallery
- Competition calendar
- Blog/news section
- Contact form
- Social media integration
- Team history timeline

## Support

For questions or modifications, refer to the code comments or contact your web developer.

---

**The Gadgeteers - FRC Team 9670**  
*The 4-H NASA Gadgeteers of St. Johns County*
