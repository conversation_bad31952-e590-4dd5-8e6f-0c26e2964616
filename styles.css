/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.team-name {
    font-weight: 700;
    font-size: 1.2rem;
    color: #f59e0b;
}

.team-number {
    font-weight: 500;
    color: #64748b;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #f59e0b;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 120px 20px 80px;
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 30%, #fbbf24 70%, #f59e0b 100%);
    color: white;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #fff, #e2e8f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #e2e8f0;
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #cbd5e1;
}

.hero-tagline {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    color: #e2e8f0;
    font-style: italic;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-primary {
    background: #f59e0b;
    color: #000000;
    border: 2px solid #f59e0b;
    font-weight: 700;
}

.btn-primary:hover {
    background: #fbbf24;
    color: #000000;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: #f59e0b;
    border: 2px solid #f59e0b;
    font-weight: 700;
}

.btn-secondary:hover {
    background: #f59e0b;
    color: #000000;
    transform: translateY(-2px);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.robot-placeholder {
    width: 300px;
    height: 300px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.robot-placeholder i {
    font-size: 8rem;
    color: rgba(255, 255, 255, 0.8);
}

/* Section Styles */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 3rem;
    color: #1e293b;
}

/* Mission Section */
.mission {
    padding: 80px 20px;
    background: #f8fafc;
}

.mission-statement {
    font-size: 1.2rem;
    line-height: 1.8;
    text-align: center;
    margin-bottom: 3rem;
    color: #475569;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.mission-values {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.value-item {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.value-item:hover {
    transform: translateY(-5px);
}

.value-item i {
    font-size: 3rem;
    color: #f59e0b;
    margin-bottom: 1rem;
}

.value-item h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1e293b;
}

.value-item p {
    color: #64748b;
}

/* Teams Section */
.teams {
    padding: 80px 20px;
    background: white;
}

.team-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
}

.tab-button {
    padding: 12px 24px;
    border: 2px solid #e2e8f0;
    background: white;
    color: #64748b;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.tab-button.active,
.tab-button:hover {
    background: #f59e0b;
    color: #000000;
    border-color: #f59e0b;
    font-weight: 700;
}

.team-content {
    display: none;
}

.team-content.active {
    display: block;
}

.team-year {
    font-size: 2rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: 2rem;
    color: #f59e0b;
}

.team-description {
    text-align: center;
    font-size: 1.1rem;
    color: #64748b;
    margin-bottom: 2rem;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.member-card {
    background: #f8fafc;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.member-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.member-info h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1e293b;
}

.role {
    color: #f59e0b;
    font-weight: 600;
    font-size: 0.95rem;
}

/* Accomplishments Section */
.accomplishments {
    padding: 80px 20px;
    background: #f8fafc;
}

.accomplishments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    max-width: 900px;
    margin: 0 auto;
}

.achievement-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    position: relative;
    overflow: hidden;
}

.achievement-card:hover {
    transform: translateY(-5px);
}

.achievement-year {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: #000000;
    color: #f59e0b;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 700;
    font-size: 0.9rem;
    border: 2px solid #f59e0b;
}

.achievement-content {
    text-align: center;
}

.achievement-content i {
    font-size: 3rem;
    color: #f59e0b;
    margin-bottom: 1rem;
}

.achievement-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1e293b;
}

.achievement-status {
    font-size: 1.1rem;
    font-weight: 700;
    color: #f59e0b;
    margin-bottom: 1rem;
}

.achievement-description {
    color: #64748b;
    line-height: 1.6;
}

/* Contact Section */
.contact {
    padding: 80px 20px;
    background: white;
}

.contact-content {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.contact-info h3 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1e293b;
}

.contact-info p {
    font-size: 1.1rem;
    color: #64748b;
    margin-bottom: 2rem;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.contact-item i {
    color: #f59e0b;
    width: 20px;
}

.contact-item a {
    color: #f59e0b;
    text-decoration: none;
    font-weight: 600;
}

.contact-item a:hover {
    text-decoration: underline;
}

/* Footer */
.footer {
    background: #000000;
    color: #f59e0b;
    padding: 2rem 20px;
    text-align: center;
    border-top: 3px solid #f59e0b;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
}

.footer-logo {
    margin-bottom: 1rem;
}

.footer-logo .team-name {
    color: #f59e0b;
    font-size: 1.3rem;
    font-weight: 700;
}

.footer-logo .team-number {
    color: #fbbf24;
    font-weight: 600;
}

.footer-text p {
    color: #d1d5db;
    margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.5rem;
    }
    
    .accomplishments-grid {
        grid-template-columns: 1fr;
    }
    
    .team-grid {
        grid-template-columns: 1fr;
    }
    
    .mission-values {
        grid-template-columns: 1fr;
    }
}
